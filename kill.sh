#!/bin/bash

PORTS=(9501 9502 9503)


# 检查端口是否被占用的函数
check_port() {
  local port=$1
  # 对于 Linux 系统:
  local pids=$(netstat -tulnp 2>/dev/null | grep ":$port " | awk '{print $7}' | cut -d'/' -f1)
  # 对于 macOS 系统 (如果 netstat 不适用，可以尝试 lsof):
  # local pids=$(lsof -ti tcp:"$port" 2>/dev/null)

  if [ -z "$pids" ]; then
    return 1  # 端口未被占用
  else
    return 0  # 端口被占用
  fi
}

# 终止端口进程的函数
kill_port_processes() {
  local port=$1
  local retry_count=$2

  echo "正在检查端口: $port (第 $retry_count 次尝试)"

  # 查找使用指定端口的进程ID (PID)
  # 对于 Linux 系统:
  PIDS=$(netstat -tulnp 2>/dev/null | grep ":$port " | awk '{print $7}' | cut -d'/' -f1)
  # 对于 macOS 系统 (如果 netstat 不适用，可以尝试 lsof):
  # PIDS=$(lsof -ti tcp:"$port" 2>/dev/null)

  if [ -z "$PIDS" ]; then
    echo "端口 $port 没有找到正在运行的进程。"
    return 0
  else
    for PID in $PIDS
    do
      if [[ "$PID" =~ ^[0-9]+$ ]]; then # 确保 PID 是一个数字
        echo "找到进程 PID: $PID 在端口 $port 上。正在终止..."
        kill -9 "$PID" # 使用 kill -9 强制终止进程
        if [ $? -eq 0 ]; then
          echo "进程 PID: $PID 已成功终止。"
        else
          echo "终止进程 PID: $PID 失败。"
        fi
      else
        echo "在端口 $port 上找到无效的 PID: '$PID'"
      fi
    done

    # 等待进程完全终止
    sleep 1

    # 检查端口是否真正释放
    if check_port "$port"; then
      echo "警告: 端口 $port 仍被占用"
      return 1
    else
      echo "端口 $port 已成功释放"
      return 0
    fi
  fi
}

# 等待9501端口释放的函数
wait_for_9501_exit() {
  local max_wait=30  # 最大等待30秒
  local wait_count=0

  echo "正在等待端口9501释放..."

  while [ $wait_count -lt $max_wait ]; do
    if ! check_port 9501; then
      echo "端口9501已释放，继续执行kill操作"
      return 0
    fi

    echo "端口9501仍被占用，等待中... (${wait_count}/${max_wait})"
    sleep 1
    wait_count=$((wait_count + 1))
  done

  echo "警告: 等待端口9501释放超时，继续执行kill操作"
  return 1
}

# 先等待9501端口释放
#wait_for_9501_exit

#echo "正在查找并终止使用以下端口的进程: ${PORTS[*]}"

for port in "${PORTS[@]}"
do
  # 第一次尝试
  if kill_port_processes "$port" 1; then
    continue  # 成功，继续下一个端口
  fi

  # 如果第一次失败，重试一次
  echo "端口 $port 第一次清理失败，正在重试..."
  sleep 1

  if kill_port_processes "$port" 2; then
    echo "端口 $port 重试成功"
  else
    echo "错误: 端口 $port 经过两次尝试仍无法释放，请手动检查"
  fi

#  echo "---"
done

# 最终检查所有端口状态
echo "最终端口状态检查:"
for port in "${PORTS[@]}"
do
  if check_port "$port"; then
    echo "端口 $port: 仍被占用 ❌"
  else
    echo "端口 $port: 已释放 ✅"
  fi
done

#echo "脚本执行完毕。"