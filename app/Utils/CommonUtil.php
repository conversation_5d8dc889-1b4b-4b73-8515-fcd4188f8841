<?php

namespace App\Utils;


use Hyperf\Stringable\Str;

class CommonUtil
{
    /**
     * 递归把字段转小驼峰
     * <AUTHOR>
     * @param array $data
     * @return array
     */
    public static function recursionCamelCase(?array $data): array
    {
        $arr = [];
        if (empty($data)){
            return $arr;
        }
        foreach ($data as $key => $value) {
            $newKey = $key;
            if (is_string($key)) {
                $newKey = Str::camel($key);
            }
            if (is_array($value)) {
                $value = static::recursionCamelCase($value);
            }
            $arr[$newKey] = $value;
        }
        return $arr;
    }

    /**
     * 递归把字段转蛇形
     * <AUTHOR>
     * @param array $data
     * @return array
     */
    public static function recursionSnakeCase(array $data): array
    {
        $arr = [];
        foreach ($data as $key => $value) {
            $newKey = $key;
            if (is_string($key)) {
                $newKey = Str::snake($key);
            }
            if (is_array($value)) {
                $value = static::recursionSnakeCase($value);
            }
            $arr[$newKey] = $value;
        }
        return $arr;
    }
    /**
     * 过滤零宽字符
     * @param string|null $string $string
     * @return string
     */
    public static function filterZeroChar(?string $string): string
    {
        if (empty($string)) {
            return '';
        }
        $str = json_encode($string, true);//转换为Unicode编码

        $patterns = []; //正则表达式
        $replacements = []; //替换成的字符
        //公共
        $patterns[0] = '/®/';
        $replacements[0] = '';

        //零宽字符&#8203;
        $patterns[1] = '/&#8203;/';
        $replacements[1] = '';

        //零宽字符&#8203;
        $patterns[2] = '#\\\u200[bcde]#us';
        $replacements[2] = '';
        $str = preg_replace($patterns, $replacements, $str);
        $str = json_decode($str);//解码Unicode编码

        return $str;
    }

}