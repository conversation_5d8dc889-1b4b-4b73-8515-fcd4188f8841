<?php

namespace App\Utils;

use DateTime;
use Hyperf\Utils\Str;

class StrUtil
{

    /**
     * 删除最后一个字符 ，可以指定字符，如果不是指定字符则不删除
     * @param string|null $str
     * @param string|null $end
     * @return string
     */
    public static  function removeLastChar(?string $str,?string $end=null): string
    {
        if(empty($str)){
            return '';
        }
        if(!empty($end)){
            if(Str::endsWith($str,$end)){
                return substr($str, 0, -1);
            }
            return $str;
        }else {
            return substr($str, 0, -1);
        }
    }

    /**
     * 格式化字符串
     * format("Hello, {}! Lorem {}. Number: {}", "World", "ipsum", mt_rand());
     * @param string $format
     * @return string|null
     */
    public static function format(string $format): null|string {
        $args = func_get_args();

        for ($i = 1; $i < func_num_args(); ++$i) {
            $arg = $args[$i];
            if ($arg instanceof DateTime){
                $arg = $arg->format("Y-m-d H:i:s");
            }
            if(is_bool($arg)){
                $arg = $arg ? 'true' : 'false';
            }
            if (!is_string($arg))
                $arg = strval($arg);

            $pos = strpos($format, "{}");

            if ($pos != 0 && !$pos) {
                return null;
            }

            $format = substr_replace($format, $arg, $pos, 2);
        }

        return $format;
    }

    /**
     * 格式化字符串
     * formatArray("Hello, :name! Lorem :ipsum. Number: :number", ["name" => "World", "ipsum" => "ipsum", "number" => mt_rand()]);
     * @param string $format
     * @param mixed $array
     * @return string|null
     */

    public static function formatArray(string $format, mixed $array): null|string {
        if (!preg_match_all("(:\w+)", $format, $matches, PREG_OFFSET_CAPTURE))
            return null;

        $matches = $matches[0];

        foreach ($array as $key => $value) {
            $match = null;

            foreach ($matches as $_match) {
                if ($_match[0] == $key) {
                    $match = $_match;
                    break;
                }
            }

            if ($match === null) {
                return null;
            }

            $format = substr_replace($format, (string) $value, $match[1], strlen($key));
        }

        return $format;
    }

    /**
     * 是否包含了其中任意一个
     * @param string $haystack
     * @param array $needles
     * @return bool
     */
    public static  function containsAny(string $haystack, array $needles): bool
    {
        foreach ($needles as $needle) {
            if (Str::contains($haystack, $needle)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断字符串是否为有效的 JSON 格式
     * @param mixed $data 待检测的字符串
     * @return bool 如果是有效的 JSON 返回 true，否则返回 false
     */
    public static function isJson(mixed $data): bool
    {
        if (empty($data)) {
            return false;
        }
        $data = (string)$data;

        // 检查是否以 { 或 [ 开头
        $trimmed = trim($data);
        if (!str_starts_with($trimmed, '{') && !str_starts_with($trimmed, '[')) {
            return false;
        }

        json_decode($data);
        return json_last_error() === JSON_ERROR_NONE;
    }

}