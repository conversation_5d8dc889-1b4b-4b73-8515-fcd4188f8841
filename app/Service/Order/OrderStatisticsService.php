<?php

namespace App\Service\Order;

use App\Constants\OrderConst;
use App\Http\Admin\Request\Order\OrderStockPrepareRequest;
use App\Http\Admin\Request\Order\OrderStockPrepareProductRequest;

use App\Model\Permission\User;
use App\Repository\Order\OrderItemRepository;
use App\Repository\Goods\GoodsSkuRepository;
use App\Repository\Goods\GoodsRepository;
use App\Repository\Product\ProductRepository;
use App\Repository\Product\ProductSkuRepository;

use App\Service\IService;
use App\Utils\MathUtil;
use App\Utils\StrUtil;
use Hyperf\Database\Model\Builder;
use Hyperf\DbConnection\Db;

class OrderStatisticsService extends IService
{
    public function __construct(
        protected readonly OrderItemRepository $orderItemRepository,
        protected readonly GoodsSkuRepository $goodsSkuRepository,
        protected readonly GoodsRepository $goodsRepository,
        protected readonly ProductRepository $productRepository,
        protected readonly ProductSkuRepository $productSkuRepository,
    ) {
    }

    public function getRepository(): OrderItemRepository
    {
        return $this->orderItemRepository;
    }

    /**
     * 获取备货单数据
     * @param User $user 当前用户
     * @param OrderStockPrepareRequest $request 请求参数
     * @return array
     */
    public function getStockPrepareData(User $user, OrderStockPrepareRequest $request): array
    {
        // 获取用户有权限的店铺ID
        $userShopIds = $this->getBindShopIds($user);

        // 取交集，确保用户只能查询自己有权限的店铺
        $shopIds = array_intersect($request->shop_ids, $userShopIds);
        if (empty($shopIds)) {
            $shopIds = $userShopIds;
        }

        // 分页处理订单项数据，根据 goods_merge_type 和 sku_merge_type 合并
        $allData = [];
        $page = 1;
        $pageSize = 500;
        $endTime = date('Y-m-d H:i:s', time());

        do {
            // 分页查询未发货的订单项
            $orderItems = $this->getUnshippedOrderItemsPaginated($shopIds, $request, $page, $pageSize, $endTime);

            if (empty($orderItems)) {
                break;
            }

            // 根据合并类型处理当前页的数据
            $pageData = $this->processOrderItemsByMergeType($orderItems, $request->goods_merge_type, $request->sku_merge_type);

            // 合并到总数据中
            $allData = $this->mergeProcessedData($allData, $pageData);

            $page++;
        } while (count($orderItems) === $pageSize);

        // 根据 goods_merge_type 重新分组为层级结构
        $stockPrepareList = $this->groupByGoodsMergeType($allData, $request->goods_merge_type);

        // 计算汇总信息
        $summary = $this->calculateSummary($stockPrepareList, $allData);

        return [
            'summary' => $summary,
            'list' => $stockPrepareList,
        ];
    }

    /**
     * 分页获取未发货的订单项
     * @param array $shopIds 店铺ID数组
     * @param OrderStockPrepareRequest $request 请求参数
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    private function getUnshippedOrderItemsPaginated(array $shopIds, OrderStockPrepareRequest $request, int $page,
                                                     int $pageSize, string $endTime): array
    {
        $query = $this->orderItemRepository->getQuery();

        // 关联 orders 表获取订单信息
        $query->leftJoin('orders', 'orders.id', '=', 'order_items.order_id');

        // 查询未发货的订单项
        $query->where('order_items.order_status', OrderConst::ORDER_STATUS_PAYMENT);
        $query->where('order_items.created_at', '<', $endTime);
        $query->whereIn('order_items.shop_id', $shopIds);

        // 时间范围筛选
        if ($request->start_time || $request->end_time) {
            $timeField = 'order_items.' . $request->time_field;

            if ($request->start_time) {
                $query->where($timeField, '>=', $request->start_time);
            }

            if ($request->end_time) {
                $query->where($timeField, '<=', $request->end_time);
            }
        }

        // 商品ID筛选
        if (!empty($request->include_goods_ids) || !empty($request->exclude_goods_ids)) {
            // 需要关联 goods_skus 表来筛选商品
            $query->leftJoin('goods_skus', function ($join) {
                $join->on('goods_skus.sku_id', '=', 'order_items.sku_id')
                     ->on('goods_skus.shop_id', '=', 'order_items.shop_id');
            });

            if (!empty($request->include_goods_ids)) {
                $query->whereIn('goods_skus.goods_id', $request->include_goods_ids);
            }

            if (!empty($request->exclude_goods_ids)) {
                $query->whereNotIn('goods_skus.goods_id', $request->exclude_goods_ids);
            }
        }

        // 分页查询
        $offset = ($page - 1) * $pageSize;
        $query->offset($offset)->limit($pageSize);

        // 选择需要的字段
        $query->select([
            'order_items.id',
            'order_items.sku_id',
            'order_items.shop_id',
            'order_items.sku_num',
            'order_items.sku_price',
            'order_items.payment',
            'order_items.goods_title',
            'order_items.sku_value',
            'order_items.sku_value1',
            'order_items.sku_value2',
            'order_items.sku_pic',
            'order_items.num_iid',
            // 添加订单相关字段
            'orders.tid',
            'orders.seller_flag',
            'orders.seller_memo',
            'orders.buyer_message',
            'orders.pay_at'
        ]);

        return $query->get()->toArray();
    }

    /**
     * 根据合并类型处理订单项数据
     * @param array $orderItems 订单项数据
     * @param int $goodsMergeType 商品合并类型
     * @param int $skuMergeType 规格合并类型
     * @return array
     */
    private function processOrderItemsByMergeType(array $orderItems, int $goodsMergeType, int $skuMergeType): array
    {
        if (empty($orderItems)) {
            return [];
        }

        // 提取所有 sku_id
        $skuIds = array_unique(array_column($orderItems, 'sku_id'));

        // 查询 goods_skus 信息
        $skuRelationData = $this->getSkuRelationData($skuIds);

        $result = [];

        foreach ($orderItems as $item) {
            $skuId = $item['sku_id'];

            if (!isset($skuRelationData[$skuId])) {
                continue;
            }

            $relationData = $skuRelationData[$skuId];

            // 根据合并类型生成合并键
            $mergeKey = $this->generateMergeKey($relationData, $goodsMergeType, $skuMergeType);

            if (!isset($result[$mergeKey])) {
                $result[$mergeKey] = [
                    'merge_key' => $mergeKey,
                    'sku_id' => $skuId,
                    'goods_id' => $relationData['goods_id'],
                    'goods_sku_id' => $relationData['goods_sku_id'],
                    'goods_title' => $relationData['goods_title'],
                    'custom_title' => $relationData['custom_title'],
                    'sku_value' => $relationData['sku_value'],
                    'sku_value1' => $item['sku_value1'] ?? '', // 从订单项获取
                    'sku_value2' => $item['sku_value2'] ?? '', // 从订单项获取
                    'custom_sku_value' => $relationData['custom_sku_value'],
                    'sku_pic' => $relationData['sku_pic'],
                    'outer_id' => $relationData['outer_id'],
                    'num_iid' => $relationData['num_iid'] ?? null,
                    'outer_goods_id' => $relationData['outer_goods_id'],
                    'total_quantity' => 0,
                    'total_amount' => MathUtil::formatTo2DecimalPlaces(0),
                    'order_count' => 0,
                    'order_comments' => [], // 存储订单信息，按 tid 去重
                    'unique_tids' => [], // 用于去重的 tid 集合
                    'unique_num_iids' => [], // 用于统计商品种类的 num_iid 集合
                    'earliest_pay_at' => null, // 最早下单时间
                ];
            }

            $result[$mergeKey]['total_quantity'] += $item['sku_num'];
            $result[$mergeKey]['total_amount'] = bcadd($result[$mergeKey]['total_amount'], $item['payment'], 2);
            $result[$mergeKey]['order_count']++;

            // 收集订单信息，按 tid 去重
            $tid = $item['tid'] ?? '';
            if (!empty($tid) && !in_array($tid, $result[$mergeKey]['unique_tids'])) {
                $result[$mergeKey]['unique_tids'][] = $tid;
                if (empty($item['seller_memo'])){
                    $seller_memo = '';
                }else{
                    $seller_memo = StrUtil::isJson($item['seller_memo']) ? $item['seller_memo'] : json_encode([$item['seller_memo']], 320);
                }
                $result[$mergeKey]['order_comments'][] = [
                    'tid' => $tid,
                    'seller_flag' => $item['seller_flag'] ?? '',
                    'seller_memo' => $seller_memo,
                    'buyer_message' => $item['buyer_message'] ?? '',
                ];
            }

            // 收集 num_iid 用于统计商品种类
            $numIid = $item['num_iid'] ?? '';
            if (!empty($numIid) && !in_array($numIid, $result[$mergeKey]['unique_num_iids'])) {
                $result[$mergeKey]['unique_num_iids'][] = $numIid;
            }

            // 收集最早下单时间
            $payAt = $item['pay_at'] ?? null;
            if (!empty($payAt)) {
                if (is_null($result[$mergeKey]['earliest_pay_at']) || $payAt < $result[$mergeKey]['earliest_pay_at']) {
                    $result[$mergeKey]['earliest_pay_at'] = $payAt;
                }
            }
        }

        return $result;
    }

    /**
     * 根据合并类型生成合并键
     * @param array $relationData 关联数据
     * @param int $goodsMergeType 商品合并类型
     * @param int $skuMergeType 规格合并类型
     * @return string
     */
    private function generateMergeKey(array $relationData, int $goodsMergeType, int $skuMergeType): string
    {
        // 商品合并键
        $goodsKey = match ($goodsMergeType) {
            1 => $relationData['num_iid'] ?? '', // 根据num_iid合并
            2 => $relationData['goods_title'] ?? '', // 根据goods.goods_title合并
            3 => $relationData['outer_goods_id'] ?? '', // 根据goods.outer_goods_id合并
            default => '',
        };

        // SKU合并键
        $skuKey = match ($skuMergeType) {
            1 => $relationData['sku_id'] ?? '', // 根据sku_id合并
            2 => $relationData['sku_value'] ?? '', // 根据goods_skus.sku_value合并
            3 => $relationData['outer_id'] ?? '', // 根据goods_skus.outer_id合并
            default => $relationData['sku_id'] ?? '',
        };

        return $goodsKey . '|' . $skuKey;
    }

    /**
     * 获取SKU关联数据（只包括商品信息）
     * @param array $skuIds SKU ID数组
     * @return array
     */
    private function getSkuRelationData(array $skuIds): array
    {
        // 查询 goods_skus 和关联的 goods 信息
        $goodsSkusQuery = $this->goodsSkuRepository->getQuery()
            ->whereIn('goods_skus.sku_id', $skuIds)
            ->leftJoin('goods', 'goods.id', '=', 'goods_skus.goods_id')
            ->select([
                'goods_skus.id as goods_sku_id',
                'goods_skus.goods_id',
                'goods_skus.shop_id',
                'goods_skus.sku_id',
                'goods_skus.sku_value',
                'goods_skus.custom_sku_value',
                'goods_skus.sku_pic',
                'goods_skus.outer_id',
                'goods.goods_title',
                'goods.custom_title',
                'goods.goods_pic',
                'goods.num_iid',
                'goods.outer_goods_id'
            ]);

        $goodsSkus = $goodsSkusQuery->get()->toArray();

        // 合并数据
        $result = [];
        foreach ($goodsSkus as $goodsSku) {
            $skuId = $goodsSku['sku_id'];
            $goodsSkuId = $goodsSku['goods_sku_id'];

            $result[$skuId] = [
                'goods_sku_id' => $goodsSkuId,
                'goods_id' => $goodsSku['goods_id'],
                'sku_id' => $skuId,
                'sku_value' => $goodsSku['sku_value'],
                'custom_sku_value' => $goodsSku['custom_sku_value'],
                'sku_pic' => $goodsSku['sku_pic'],
                'outer_id' => $goodsSku['outer_id'],
                'goods_title' => $goodsSku['goods_title'],
                'custom_title' => $goodsSku['custom_title'],
                'num_iid' => $goodsSku['num_iid'],
                'outer_goods_id' => $goodsSku['outer_goods_id'],
            ];
        }

        return $result;
    }

    /**
     * 合并处理后的数据
     * @param array $allData 总数据
     * @param array $pageData 当前页数据
     * @return array
     */
    private function mergeProcessedData(array $allData, array $pageData): array
    {
        foreach ($pageData as $mergeKey => $data) {
            if (isset($allData[$mergeKey])) {
                $allData[$mergeKey]['total_quantity'] += $data['total_quantity'];
                $allData[$mergeKey]['total_amount'] = bcadd($allData[$mergeKey]['total_amount'], $data['total_amount'], 2);
                $allData[$mergeKey]['order_count'] += $data['order_count'];

                // 合并订单信息，保持 tid 去重
                if (isset($data['order_comments']) && is_array($data['order_comments'])) {
                    foreach ($data['order_comments'] as $comment) {
                        if (!in_array($comment['tid'], $allData[$mergeKey]['unique_tids'])) {
                            $allData[$mergeKey]['unique_tids'][] = $comment['tid'];
                            $allData[$mergeKey]['order_comments'][] = $comment;
                        }
                    }
                }

                // 合并 num_iid 集合
                if (isset($data['unique_num_iids']) && is_array($data['unique_num_iids'])) {
                    foreach ($data['unique_num_iids'] as $numIid) {
                        if (!in_array($numIid, $allData[$mergeKey]['unique_num_iids'])) {
                            $allData[$mergeKey]['unique_num_iids'][] = $numIid;
                        }
                    }
                }

                // 合并最早下单时间
                if (isset($data['earliest_pay_at']) && !empty($data['earliest_pay_at'])) {
                    if (is_null($allData[$mergeKey]['earliest_pay_at']) || $data['earliest_pay_at'] < $allData[$mergeKey]['earliest_pay_at']) {
                        $allData[$mergeKey]['earliest_pay_at'] = $data['earliest_pay_at'];
                    }
                }
            } else {
                $allData[$mergeKey] = $data;
            }
        }

        return $allData;
    }

    /**
     * 根据商品合并类型分组数据
     * @param array $allData 所有数据
     * @param int $goodsMergeType 商品合并类型：1=根据num_iid合并，2=根据goods.goods_title合并，3=根据goods.outer_goods_id合并
     * @param int $skuMergeType SKU合并类型
     * @return array
     */
    private function groupByGoodsMergeType(array $allData, int $goodsMergeType): array
    {
        $grouped = [];

        foreach ($allData as $data) {
            // 根据商品合并类型确定分组键和名称
            $groupKey = match ($goodsMergeType) {
                1 => $data['num_iid'] ?? '', // 根据num_iid合并
                2 => $data['goods_title'] ?? '', // 根据goods.goods_title合并
                3 => $data['outer_goods_id'] ?? '', // 根据goods.outer_goods_id合并
                default => $data['num_iid'] ?? '',
            };

            if (!isset($grouped[$groupKey])) {
                $grouped[$groupKey] = [
                    'id' => $data['goods_id'],
                    'num_iid' => $data['num_iid'] ?? '',
                    'outer_goods_id' => $data['outer_goods_id'] ?? '',
                    'earliest_pay_at' => null, // 商品级别的最早支付时间
                    'goods_pic' => $data['sku_pic'] ?? '', // 使用SKU图片作为商品图片
                    'goods_title' => $data['goods_title'],
                    'custom_title' => $data['custom_title'],
                    'total_num' => 0,
                    'total_payment' => MathUtil::formatTo2DecimalPlaces(0),
                    'skus' => []
                ];
            }


            $grouped[$groupKey]['skus'][] = [
                'id' => $data['goods_sku_id'],
                'sku_id' => $data['sku_id'],
                'sku_value' => $data['sku_value'],
                'sku_value1' => $data['sku_value1'] ?? '', // 添加 SKU 值1
                'sku_value2' => $data['sku_value2'] ?? '', // 添加 SKU 值2
                'custom_sku_value' => $data['custom_sku_value'],
                'outer_id' => $data['outer_id'],
                'outer_goods_id' => $data['outer_goods_id'] ?? '', // 添加商品外部编码
                'sku_pic' => $data['sku_pic'],
                'total_num' => $data['total_quantity'],
                'total_payment' => MathUtil::formatTo2DecimalPlaces($data['total_amount']),
                'earliest_pay_at' => $data['earliest_pay_at'] ?? null, // 添加最早下单时间
                'comments' => $data['order_comments'] ?? [], // 添加订单信息
            ];

            // 累加商品级别的总数量和总金额
            $grouped[$groupKey]['total_num'] += $data['total_quantity'];
            $grouped[$groupKey]['total_payment'] = bcadd($grouped[$groupKey]['total_payment'], $data['total_amount'], 2);
        }

        // 转换为数组并排序，对 total_payment 进行格式化
        $result = array_values($grouped);
        foreach ($result as &$item) {
            $item['total_payment'] = MathUtil::formatTo2DecimalPlaces($item['total_payment']);

            // 计算商品级别的最早支付时间（取该商品下所有SKU中最早的时间）
            $payAtTimes = collect($item['skus'])->pluck('earliest_pay_at')->filter()->values();
            $item['earliest_pay_at'] = $payAtTimes->isNotEmpty() ? $payAtTimes->min() : null;
        }

        usort($result, function ($a, $b) {
            $titleA = $a['goods_title'];
            $titleB = $b['goods_title'];
            return strcmp($titleA, $titleB);
        });

        return $result;
    }

    /**
     * 计算汇总信息
     * @param array $stockPrepareList 备货单列表
     * @param array $allData 所有原始数据
     * @return array
     */
    private function calculateSummary(array $stockPrepareList, array $allData): array
    {
        $totalSkuCount = 0;
        $totalQuantity = 0;
        $totalAmount = MathUtil::formatTo2DecimalPlaces(0);
        $totalGoodsCount = count($stockPrepareList);

        // 用于统计的集合
        $uniqueTids = [];
        $uniqueNumIids = [];
        $uniqueSkuIds = [];

        foreach ($stockPrepareList as $commodity) {
            $totalSkuCount += count($commodity['skus']);

            foreach ($commodity['skus'] as $sku) {
                $totalQuantity += $sku['total_num'];
                $totalAmount = bcadd($totalAmount, $sku['total_payment'], 2);

                // 收集唯一的 sku_id
                if (!in_array($sku['sku_id'], $uniqueSkuIds)) {
                    $uniqueSkuIds[] = $sku['sku_id'];
                }

                // 从 comments 中收集唯一的 tid
                if (isset($sku['comments']) && is_array($sku['comments'])) {
                    foreach ($sku['comments'] as $comment) {
                        if (!empty($comment['tid']) && !in_array($comment['tid'], $uniqueTids)) {
                            $uniqueTids[] = $comment['tid'];
                        }
                    }
                }
            }
        }

        // 从原始数据中收集唯一的 num_iid
        foreach ($allData as $data) {
            if (isset($data['unique_num_iids']) && is_array($data['unique_num_iids'])) {
                foreach ($data['unique_num_iids'] as $numIid) {
                    if (!in_array($numIid, $uniqueNumIids)) {
                        $uniqueNumIids[] = $numIid;
                    }
                }
            }
        }

        return [
//            'total_sku_count' => $totalSkuCount,      // 总SKU数量（规格种类）
            'total_quantity' => $totalQuantity,       // 总商品数量（总件数）
            'total_amount' => MathUtil::formatTo2DecimalPlaces($totalAmount), // 总实付金额
//            'total_goods_count' => $totalGoodsCount,  // 总商品
            'total_order_count' => count($uniqueTids), // 订单数（tid 去重）
            'total_goods_type_count' => count($uniqueNumIids), // 商品种类（num_iid 去重）
            'total_sku_type_count' => count($uniqueSkuIds), // 规格种类（sku_id 去重）
        ];
    }

    /**
     * 获取备货单数据(货品维度)
     * @param User $user 当前用户
     * @param OrderStockPrepareProductRequest $request 请求参数
     * @return array
     */
    public function getStockPrepareProductData(User $user, OrderStockPrepareProductRequest $request): array
    {
        // 获取用户有权限的店铺ID
        $userShopIds = $this->getBindShopIds($user);

        // 取交集，确保用户只能查询自己有权限的店铺
        $shopIds = array_intersect($request->shop_ids, $userShopIds);
        if (empty($shopIds)) {
            $shopIds = $userShopIds;
        }

        // 分页处理订单项数据，根据 product_merge_type 和 sku_merge_type 合并
        $allData = [];
        $page = 1;
        $pageSize = 500;
        $endTime = date('Y-m-d H:i:s', time());

        do {
            // 分页查询未发货的订单项
            $orderItems = $this->getUnshippedOrderItemsForProductPaginated($shopIds, $request, $page, $pageSize, $endTime);

            if (empty($orderItems)) {
                break;
            }

            // 根据合并类型处理当前页的数据
            $pageData = $this->processOrderItemsByProductMergeType($orderItems, $request->product_merge_type, $request->sku_merge_type);

            // 合并到总数据中
            $allData = $this->mergeProcessedProductData($allData, $pageData);

            $page++;
        } while (count($orderItems) === $pageSize);

        // 根据 product_merge_type 重新分组为层级结构
        $stockPrepareList = $this->groupByProductMergeType($allData, $request->product_merge_type);

        // 计算汇总信息
        $summary = $this->calculateProductSummary($stockPrepareList, $allData);

        return [
            'summary' => $summary,
            'list' => $stockPrepareList,
        ];
    }

    /**
     * 分页获取未发货的订单项(货品维度)
     * @param array $shopIds 店铺ID数组
     * @param OrderStockPrepareProductRequest $request 请求参数
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @param string $endTime 结束时间
     * @return array
     */
    private function getUnshippedOrderItemsForProductPaginated(array $shopIds, OrderStockPrepareProductRequest $request, int $page,
                                                               int $pageSize, string $endTime): array
    {
        $query = $this->orderItemRepository->getQuery();

        // 关联 orders 表获取订单信息
        $query->leftJoin('orders', 'orders.id', '=', 'order_items.order_id');

        // 查询未发货的订单项
        $query->where('order_items.order_status', OrderConst::ORDER_STATUS_PAYMENT);
        $query->where('order_items.created_at', '<', $endTime);
        $query->whereIn('order_items.shop_id', $shopIds);

        // 时间范围筛选
        if ($request->start_time || $request->end_time) {
            $timeField = 'order_items.' . $request->time_field;

            if ($request->start_time) {
                $query->where($timeField, '>=', $request->start_time);
            }

            if ($request->end_time) {
                $query->where($timeField, '<=', $request->end_time);
            }
        }

        // 货品ID筛选 - 通过 goods_product_relations 表关联
        if (!empty($request->include_product_ids) || !empty($request->exclude_product_ids)) {
            // 需要关联 goods_skus 和 goods_product_relations 表来筛选货品
            $query->leftJoin('goods_skus', function ($join) {
                $join->on('goods_skus.sku_id', '=', 'order_items.sku_id')
                     ->on('goods_skus.shop_id', '=', 'order_items.shop_id');
            });

            $query->leftJoin('goods_product_relations', function ($join) {
                $join->on('goods_product_relations.goods_sku_id', '=', 'goods_skus.id');
            });

            if (!empty($request->include_product_ids)) {
                $query->whereIn('goods_product_relations.product_id', $request->include_product_ids);
            }

            if (!empty($request->exclude_product_ids)) {
                $query->whereNotIn('goods_product_relations.product_id', $request->exclude_product_ids);
            }
        }

        // 分页查询
        $offset = ($page - 1) * $pageSize;
        $query->offset($offset)->limit($pageSize);

        // 选择需要的字段
        $query->select([
            'order_items.id',
            'order_items.sku_id',
            'order_items.shop_id',
            'order_items.sku_num',
            'order_items.sku_price',
            'order_items.payment',
            'order_items.goods_title',
            'order_items.sku_value',
            'order_items.sku_value1',
            'order_items.sku_value2',
            'order_items.sku_pic',
            'order_items.num_iid',
            // 添加订单相关字段
            'orders.tid',
            'orders.seller_flag',
            'orders.seller_memo',
            'orders.buyer_message',
            'orders.pay_at'
        ]);

        return $query->get()->toArray();
    }

    /**
     * 根据货品合并类型处理订单项数据
     * @param array $orderItems 订单项数据
     * @param int $productMergeType 货品合并类型
     * @param int $skuMergeType 规格合并类型
     * @return array
     */
    private function processOrderItemsByProductMergeType(array $orderItems, int $productMergeType, int $skuMergeType): array
    {
        if (empty($orderItems)) {
            return [];
        }

        // 提取所有 sku_id
        $skuIds = array_unique(array_column($orderItems, 'sku_id'));

        // 查询 product_skus 信息
        $skuRelationData = $this->getProductSkuRelationData($skuIds);

        $result = [];

        foreach ($orderItems as $item) {
            $skuId = $item['sku_id'];

            if (!isset($skuRelationData[$skuId])) {
                continue;
            }

            $relationData = $skuRelationData[$skuId];

            // 根据合并类型生成合并键
            $mergeKey = $this->generateProductMergeKey($relationData, $productMergeType, $skuMergeType);

            if (!isset($result[$mergeKey])) {
                $result[$mergeKey] = [
                    'merge_key' => $mergeKey,
                    'sku_id' => $skuId,
                    'product_id' => $relationData['product_id'],
                    'product_sku_id' => $relationData['product_sku_id'],
                    'product_name' => $relationData['product_name'],
                    'product_no' => $relationData['product_no'],
                    'sku_name' => $relationData['sku_name'],
                    'sku_short_name' => $relationData['sku_short_name'],
                    'merchant_code' => $relationData['merchant_code'],
                    'sku_value1' => $item['sku_value1'] ?? '', // 从订单项获取
                    'sku_value2' => $item['sku_value2'] ?? '', // 从订单项获取
                    'image_url' => $relationData['image_url'],
                    'barcode' => $relationData['barcode'],
                    'total_quantity' => 0,
                    'total_amount' => MathUtil::formatTo2DecimalPlaces(0),
                    'order_count' => 0,
                    'order_comments' => [], // 存储订单信息，按 tid 去重
                    'unique_tids' => [], // 用于去重的 tid 集合
                    'unique_num_iids' => [], // 用于统计商品种类的 num_iid 集合
                    'earliest_pay_at' => null, // 最早下单时间
                ];
            }

            $result[$mergeKey]['total_quantity'] += $item['sku_num'];
            $result[$mergeKey]['total_amount'] = bcadd($result[$mergeKey]['total_amount'], $item['payment'], 2);
            $result[$mergeKey]['order_count']++;

            // 收集订单信息，按 tid 去重
            $tid = $item['tid'] ?? '';
            if (!empty($tid) && !in_array($tid, $result[$mergeKey]['unique_tids'])) {
                $result[$mergeKey]['unique_tids'][] = $tid;
                if (empty($item['seller_memo'])){
                    $seller_memo = '';
                }else{
                    $seller_memo = StrUtil::isJson($item['seller_memo']) ? $item['seller_memo'] : json_encode([$item['seller_memo']], 320);
                }
                $result[$mergeKey]['order_comments'][] = [
                    'tid' => $tid,
                    'seller_flag' => $item['seller_flag'] ?? '',
                    'seller_memo' => $seller_memo,
                    'buyer_message' => $item['buyer_message'] ?? '',
                ];
            }

            // 收集 num_iid 用于统计商品种类
            $numIid = $item['num_iid'] ?? '';
            if (!empty($numIid) && !in_array($numIid, $result[$mergeKey]['unique_num_iids'])) {
                $result[$mergeKey]['unique_num_iids'][] = $numIid;
            }

            // 收集最早下单时间
            $payAt = $item['pay_at'] ?? null;
            if (!empty($payAt)) {
                if (is_null($result[$mergeKey]['earliest_pay_at']) || $payAt < $result[$mergeKey]['earliest_pay_at']) {
                    $result[$mergeKey]['earliest_pay_at'] = $payAt;
                }
            }
        }

        return $result;
    }

    /**
     * 获取货品SKU关联数据
     * @param array $skuIds SKU ID数组
     * @return array
     */
    private function getProductSkuRelationData(array $skuIds): array
    {
        // 通过 goods_product_relations 表关联查询 product_skus 和 products 信息
        $query = $this->productSkuRepository->getQuery()
            ->leftJoin('goods_product_relations', 'goods_product_relations.product_sku_id', '=', 'product_skus.id')
            ->leftJoin('goods_skus', 'goods_skus.id', '=', 'goods_product_relations.goods_sku_id')
            ->leftJoin('products', 'products.id', '=', 'product_skus.product_id')
            ->whereIn('goods_skus.sku_id', $skuIds)
            ->select([
                'product_skus.id as product_sku_id',
                'product_skus.product_id',
                'product_skus.sku_name',
                'product_skus.sku_short_name',
                'product_skus.merchant_code',
                'product_skus.image_url',
                'product_skus.barcode',
                'products.name as product_name',
                'products.product_no',
                'goods_skus.sku_id',
            ]);

        $productSkus = $query->get()->toArray();

        // 合并数据
        $result = [];
        foreach ($productSkus as $productSku) {
            $skuId = $productSku['sku_id'];

            $result[$skuId] = [
                'product_sku_id' => $productSku['product_sku_id'],
                'product_id' => $productSku['product_id'],
                'sku_id' => $skuId,
                'sku_name' => $productSku['sku_name'],
                'sku_short_name' => $productSku['sku_short_name'],
                'merchant_code' => $productSku['merchant_code'],
                'image_url' => $productSku['image_url'],
                'barcode' => $productSku['barcode'],
                'product_name' => $productSku['product_name'],
                'product_no' => $productSku['product_no'],
            ];
        }

        return $result;
    }

    /**
     * 根据货品合并类型生成合并键
     * @param array $relationData 关联数据
     * @param int $productMergeType 货品合并类型
     * @param int $skuMergeType 规格合并类型
     * @return string
     */
    private function generateProductMergeKey(array $relationData, int $productMergeType, int $skuMergeType): string
    {
        // 货品合并键
        $productKey = match ($productMergeType) {
            1 => $relationData['product_no'] ?? '', // 根据product_no合并
            2 => $relationData['product_name'] ?? '', // 根据products.name合并
            default => '',
        };

        // SKU合并键
        $skuKey = match ($skuMergeType) {
            1 => $relationData['product_sku_id'] ?? '', // 根据product_sku_id合并
            2 => $relationData['sku_name'] ?? '', // 根据product_skus.sku_name合并
            3 => $relationData['merchant_code'] ?? '', // 根据product_skus.merchant_code合并
            default => $relationData['product_sku_id'] ?? '',
        };

        return $productKey . '|' . $skuKey;
    }

    /**
     * 合并处理后的货品数据
     * @param array $allData 总数据
     * @param array $pageData 当前页数据
     * @return array
     */
    private function mergeProcessedProductData(array $allData, array $pageData): array
    {
        foreach ($pageData as $mergeKey => $data) {
            if (isset($allData[$mergeKey])) {
                $allData[$mergeKey]['total_quantity'] += $data['total_quantity'];
                $allData[$mergeKey]['total_amount'] = bcadd($allData[$mergeKey]['total_amount'], $data['total_amount'], 2);
                $allData[$mergeKey]['order_count'] += $data['order_count'];

                // 合并订单信息，保持 tid 去重
                if (isset($data['order_comments']) && is_array($data['order_comments'])) {
                    foreach ($data['order_comments'] as $comment) {
                        if (!in_array($comment['tid'], $allData[$mergeKey]['unique_tids'])) {
                            $allData[$mergeKey]['unique_tids'][] = $comment['tid'];
                            $allData[$mergeKey]['order_comments'][] = $comment;
                        }
                    }
                }

                // 合并 num_iid 集合
                if (isset($data['unique_num_iids']) && is_array($data['unique_num_iids'])) {
                    foreach ($data['unique_num_iids'] as $numIid) {
                        if (!in_array($numIid, $allData[$mergeKey]['unique_num_iids'])) {
                            $allData[$mergeKey]['unique_num_iids'][] = $numIid;
                        }
                    }
                }

                // 合并最早下单时间
                if (isset($data['earliest_pay_at']) && !empty($data['earliest_pay_at'])) {
                    if (is_null($allData[$mergeKey]['earliest_pay_at']) || $data['earliest_pay_at'] < $allData[$mergeKey]['earliest_pay_at']) {
                        $allData[$mergeKey]['earliest_pay_at'] = $data['earliest_pay_at'];
                    }
                }
            } else {
                $allData[$mergeKey] = $data;
            }
        }

        return $allData;
    }

    /**
     * 根据货品合并类型分组数据
     * @param array $allData 所有数据
     * @param int $productMergeType 货品合并类型：1=根据product_no合并，2=根据products.name合并
     * @return array
     */
    private function groupByProductMergeType(array $allData, int $productMergeType): array
    {
        $grouped = [];

        foreach ($allData as $data) {
            // 根据货品合并类型确定分组键和名称
            $groupKey = match ($productMergeType) {
                1 => $data['product_no'] ?? '', // 根据product_no合并
                2 => $data['product_name'] ?? '', // 根据products.name合并
                default => $data['product_no'] ?? '',
            };

            if (!isset($grouped[$groupKey])) {
                $grouped[$groupKey] = [
                    'id' => $data['product_id'],
                    'product_no' => $data['product_no'] ?? '',
                    'product_name' => $data['product_name'] ?? '',
                    'earliest_pay_at' => null, // 货品级别的最早支付时间
                    'product_pic' => $data['image_url'] ?? '', // 使用SKU图片作为货品图片
                    'total_num' => 0,
                    'total_payment' => MathUtil::formatTo2DecimalPlaces(0),
                    'skus' => []
                ];
            }

            $grouped[$groupKey]['skus'][] = [
                'id' => $data['product_sku_id'],
                'sku_id' => $data['sku_id'],
                'sku_name' => $data['sku_name'],
                'sku_short_name' => $data['sku_short_name'],
                'sku_value1' => $data['sku_value1'] ?? '', // 添加 SKU 值1
                'sku_value2' => $data['sku_value2'] ?? '', // 添加 SKU 值2
                'merchant_code' => $data['merchant_code'],
                'barcode' => $data['barcode'],
                'image_url' => $data['image_url'],
                'total_num' => $data['total_quantity'],
                'total_payment' => MathUtil::formatTo2DecimalPlaces($data['total_amount']),
                'earliest_pay_at' => $data['earliest_pay_at'] ?? null, // 添加最早下单时间
                'comments' => $data['order_comments'] ?? [], // 添加订单信息
            ];

            // 累加货品级别的总数量和总金额
            $grouped[$groupKey]['total_num'] += $data['total_quantity'];
            $grouped[$groupKey]['total_payment'] = bcadd($grouped[$groupKey]['total_payment'], $data['total_amount'], 2);
        }

        // 转换为数组并排序，对 total_payment 进行格式化
        $result = array_values($grouped);
        foreach ($result as &$item) {
            $item['total_payment'] = MathUtil::formatTo2DecimalPlaces($item['total_payment']);

            // 计算货品级别的最早支付时间（取该货品下所有SKU中最早的时间）
            $payAtTimes = collect($item['skus'])->pluck('earliest_pay_at')->filter()->values();
            $item['earliest_pay_at'] = $payAtTimes->isNotEmpty() ? $payAtTimes->min() : null;
        }

        usort($result, function ($a, $b) {
            $nameA = $a['product_name'];
            $nameB = $b['product_name'];
            return strcmp($nameA, $nameB);
        });

        return $result;
    }

    /**
     * 计算货品汇总信息
     * @param array $stockPrepareList 备货单列表
     * @param array $allData 所有原始数据
     * @return array
     */
    private function calculateProductSummary(array $stockPrepareList, array $allData): array
    {
        $totalSkuCount = 0;
        $totalQuantity = 0;
        $totalAmount = MathUtil::formatTo2DecimalPlaces(0);
        $totalProductCount = count($stockPrepareList);

        // 用于统计的集合
        $uniqueTids = [];
        $uniqueNumIids = [];
        $uniqueSkuIds = [];

        foreach ($stockPrepareList as $product) {
            $totalSkuCount += count($product['skus']);

            foreach ($product['skus'] as $sku) {
                $totalQuantity += $sku['total_num'];
                $totalAmount = bcadd($totalAmount, $sku['total_payment'], 2);

                // 收集唯一的 sku_id
                if (!in_array($sku['sku_id'], $uniqueSkuIds)) {
                    $uniqueSkuIds[] = $sku['sku_id'];
                }

                // 从 comments 中收集唯一的 tid
                if (isset($sku['comments']) && is_array($sku['comments'])) {
                    foreach ($sku['comments'] as $comment) {
                        if (!empty($comment['tid']) && !in_array($comment['tid'], $uniqueTids)) {
                            $uniqueTids[] = $comment['tid'];
                        }
                    }
                }
            }
        }

        // 从原始数据中收集唯一的 num_iid
        foreach ($allData as $data) {
            if (isset($data['unique_num_iids']) && is_array($data['unique_num_iids'])) {
                foreach ($data['unique_num_iids'] as $numIid) {
                    if (!in_array($numIid, $uniqueNumIids)) {
                        $uniqueNumIids[] = $numIid;
                    }
                }
            }
        }

        return [
//            'total_sku_count' => $totalSkuCount,      // 总SKU数量（规格种类）
            'total_quantity' => $totalQuantity,       // 总商品数量（总件数）
            'total_amount' => MathUtil::formatTo2DecimalPlaces($totalAmount), // 总实付金额
//            'total_product_count' => $totalProductCount,  // 总货品数
            'total_order_count' => count($uniqueTids), // 订单数（tid 去重）
            'total_goods_type_count' => count($uniqueNumIids), // 商品种类（num_iid 去重）
            'total_sku_type_count' => count($uniqueSkuIds), // 规格种类（sku_id 去重）
        ];
    }
}
