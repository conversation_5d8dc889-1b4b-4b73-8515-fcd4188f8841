<?php

namespace App\Http\Admin\Controller\Order;

use App\Http\Admin\Controller\AbstractController;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Admin\Request\Order\OrderStockPrepareRequest;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Service\Order\OrderStatisticsService;
use Hyperf\Swagger\Annotation as OA;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\Swagger\Annotation\Post;
use Mine\Access\Attribute\Permission;
use Mine\Swagger\Attributes\ResultResponse;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\RequestBody;


#[OA\Tag('{订单统计}')]
#[OA\HyperfServer('http')]
//#[Controller(prefix: '/admin')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
final class OrderStatisticsController extends AbstractController
{
    public function __construct(
        private readonly OrderStatisticsService $orderStatisticsService,
        private readonly CurrentUser $currentUser
    ) {
    }

    #[Post(
        path: '/admin/order/statistics/stockPrepare',
        operationId: 'order:statistics:stockPrepare',
        summary: '备货单统计(商品维度)',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['订单统计'],
    )]
    #[RequestBody(content: new JsonContent(ref: OrderStockPrepareRequest::class, title: '备货单查询'))]
//    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'order:statistics:stockPrepare')]
    public function stockPrepare(OrderStockPrepareRequest $request): Result
    {
        $newRequest = $request->validatedAndAssignNew();
        $result = $this->orderStatisticsService->getStockPrepareData(
            $this->currentUser->user(),
            $newRequest
        );

        return $this->success($result);
    }
}